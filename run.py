#!/usr/bin/env python3
import tkinter as tk
import sys
import os

# 确保可以导入src包
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.ui.stock_kline.app import StockKLineApp
from src.ui.controllers.main_controller import MainController as ExcelViewerApp
from src.cli.excel_processor import ExcelProcessor, BatchProcessor
from src.cli.argument_parser import ArgumentParser
from src.cli.stock_manager import StockManager

def main():
    """主函数，解析命令行参数并启动应用程序"""
    config = ArgumentParser().parse_args()

    if config.add_stock or config.remove_stock or config.list_stocks:
        manager = StockManager()
        if config.add_stock:
            manager.add_stock(config.add_stock, config.category)
        elif config.remove_stock:
            manager.remove_stock(config.remove_stock, config.category)
        elif config.list_stocks:
            manager.list_stocks(config.category)
        sys.exit(0)

    # 处理CSV输入模式
    if config.calc_daily_change_from_csv_path:
        print(f"检测到CSV输入模式，文件: {config.calc_daily_change_from_csv_path}")
        processor = ExcelProcessor(config=config)
        processor.process_csv_for_daily_change(config.calc_daily_change_from_csv_path)
        print("CSV处理完成。")
        sys.exit(0)

    if config.mode == 'gui':
        root = tk.Tk()
        app = StockKLineApp(root)
        root.mainloop()
    elif config.mode == 'excel':
        root = tk.Tk()
        app = ExcelViewerApp(root)
        root.mainloop()
    else: # CLI mode
        stock_codes_to_process = []
        is_batch_mode = False

        if config.is_stock:
            if config.category_input and config.input_path:
                print("错误：--input 和 --category-input 参数不能同时使用。", file=sys.stderr)
                sys.exit(1)
            elif config.category_input:
                print(f"从分类 '{config.category_input}' 加载股票...")
                manager = StockManager()
                stock_codes_to_process = manager.get_stocks_by_category(config.category_input)
                if not stock_codes_to_process:
                    print(f"错误：分类 '{config.category_input}' 中没有找到股票或分类不存在。", file=sys.stderr)
                    sys.exit(1)
                print(f"找到 {len(stock_codes_to_process)} 只股票: {', '.join(stock_codes_to_process)}")
                is_batch_mode = True
            elif config.input_path:
                stock_codes_to_process = [code.strip() for code in config.input_path.split(',')]
                if not stock_codes_to_process or not any(stock_codes_to_process):
                    print("错误：--input 参数提供的股票代码为空或无效。", file=sys.stderr)
                    sys.exit(1)
                if len(stock_codes_to_process) > 1:
                    is_batch_mode = True
            else:
                print("错误：CLI模式下使用 --stock 时，必须指定 --input 或 --category-input。", file=sys.stderr)
                sys.exit(1)
        elif not config.input_path: # 非 --stock 模式，即处理 Excel 文件
             print("错误：CLI模式下必须指定 --input (Excel文件路径)。", file=sys.stderr)
             sys.exit(1)

        processor = ExcelProcessor(config=config)
 
        if is_batch_mode:
            batch_processor = BatchProcessor(
                max_concurrent=config.batch_size,
                delay=config.batch_delay,
                config=config
            )
            combined_df, _, _ = batch_processor.process_batch(
                stock_codes_to_process,
                config.period,
                config.interval,
                config.data_source
            )
            if combined_df.empty:
                print("错误：批量处理未能获取任何有效数据。", file=sys.stderr)
                sys.exit(1)
            processor.filtered_df = combined_df
        elif config.is_stock: # 单个股票处理
            stock_code = stock_codes_to_process[0]
            if not processor.load_stock_data(stock_code, config.period, config.interval, config.data_source):
                sys.exit(1)
        else: # 单个 Excel 文件处理
            if not processor.load_file(config.input_path):
                sys.exit(1)

        # 应用筛选条件 (对单个或批量结果都适用)
        if config.filters:
            filters_to_apply = []
            for col, op, val in config.filters:
                if op in ['大于', '>']: op = '>'
                elif op in ['小于', '<']: op = '<'
                elif op in ['等于', '=']: op = '='
                elif op in ['包含', ':']: op = ':'
                else:
                    print(f"警告：不支持的筛选操作符 '{op}'，跳过此条件。", file=sys.stderr)
                    continue
                filters_to_apply.append(f"{col}{op}{val}")
            if filters_to_apply and not processor.apply_filter(filters_to_apply):
                sys.exit(1)

        # 执行左侧打分分析 (对单个或批量结果都适用)
        if config.left_score:
            if not processor.apply_left_score_analysis():
                sys.exit(1)

        # 执行技术分析 (对单个或批量结果都适用)
        if config.analysis_type:
            relevant_analysis_params = {
                'days': config.days,
                'ma_period': config.ma_period,
                'trend': config.trend,
                'min_score': config.min_score,
                'verbose': config.verbose,
                'period': config.period,
                'interval': config.interval,
                'data_source': config.data_source,
                'realtime': config.realtime
            }
            if not processor.apply_technical_analysis(config.analysis_type, **relevant_analysis_params):
                sys.exit(1)
            
            if not is_batch_mode and config.analysis_type == 'above_cloud' and config.signal_dates:
                print("\n执行交易模拟...")
                if not processor.simulate_trading(config.signal_dates, config.quantity):
                    sys.exit(1)

        if config.output_path:
            if not processor.export_data(config.output_path):
                sys.exit(1)
        else:
            if processor.filtered_df is not None and not processor.filtered_df.empty:
                processor.export_data()
            elif not config.add_stock and not config.remove_stock and not config.list_stocks:
                 print("\n处理完成。无明确操作或输出指定。")


if __name__ == "__main__":
    main()